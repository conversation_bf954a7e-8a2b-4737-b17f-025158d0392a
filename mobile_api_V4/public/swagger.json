{"openapi": "3.0.3", "info": {"description": "<PERSON>", "title": "Be Strong API List", "version": "4.0"}, "servers": [{"url": "http://localhost/bestrong/mobile_api_V4/public/api"}], "tags": [{"name": "Mobile API", "description": "All mobile endpoints"}], "paths": {"/app-notification": {"post": {"description": "API to get the app update notification", "operationId": "appUpdateNotification", "tags": ["Mobile API"], "parameters": [{"name": "version", "description": "Current App Version", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "platform", "description": "Device Platform (`android` or `ios`)", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "device_id", "description": "Unique Device ID", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppNotification"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppNotification"}}}}, "400": {"description": "Validation Failed.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationFailed"}}}}}}}, "/signup": {"post": {"description": "Api to add new users in to the app", "operationId": "signupUser", "tags": ["Mobile API"], "parameters": [{"description": "UserType (1-Student, 2-<PERSON>, 3-<PERSON><PERSON>, 4-School admin, 5-Supporter)", "in": "query", "name": "user_type", "required": true, "schema": {"type": "string"}}, {"description": "Username", "in": "query", "name": "username", "required": true, "schema": {"type": "string"}}, {"description": "Name", "in": "query", "name": "name", "required": true, "schema": {"type": "string"}}, {"description": "Email <PERSON>d", "in": "query", "name": "email_id", "required": true, "schema": {"type": "string"}}, {"description": "Password", "in": "query", "name": "password", "required": true, "schema": {"type": "string"}}, {"description": "Confirm Password", "in": "query", "name": "confirm_password", "required": true, "schema": {"type": "string"}}, {"description": "Date of birth (2015-05-01)", "in": "query", "name": "dob", "required": true, "schema": {"type": "string"}}, {"description": "Gender ( 1-male, 0-female )", "in": "query", "name": "gender", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/signupviasocialmedia"}}}}, "responses": {"200": {"description": "New user has been created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/signupviasocialmedia"}}}}, "500": {"description": "Something went wrong.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalError"}}}}, "201": {"description": "Email address already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/emailexists"}}}}}}}, "/signupviasocialmedia": {"post": {"description": "Api to add new users in to the app via Social Media", "operationId": "signupViaSocialMedia", "tags": ["Mobile API"], "parameters": [{"description": "UserType (1-Student, 2-Teacher, 3-<PERSON><PERSON>, 4-School admin)", "in": "query", "name": "user_type", "required": true, "schema": {"type": "string"}}, {"description": "Username", "in": "query", "name": "username", "required": true, "schema": {"type": "string"}}, {"description": "Name", "in": "query", "name": "name", "required": true, "schema": {"type": "string"}}, {"description": "Email <PERSON>d", "in": "query", "name": "email_id", "required": true, "schema": {"type": "string"}}, {"description": "Date of birth (2015-05-01)", "in": "query", "name": "dob", "required": true, "schema": {"type": "string"}}, {"description": "Gender ( 1-male, 0-female )", "in": "query", "name": "gender", "required": true, "schema": {"type": "string"}}, {"description": "Media Name ( 1- website 2- facebook 3-twitter 4-gplus)", "in": "query", "name": "media_name", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/signup"}}}}, "responses": {"200": {"description": "New user has been created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/signup"}}}}, "500": {"description": "Something went wrong.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalError"}}}}, "201": {"description": "Email address already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/emailexists"}}}}}}}, "/verifyuser": {"post": {"description": "Api to verify new users in the app", "operationId": "verifyUser", "tags": ["Mobile API"], "parameters": [{"description": "OTP", "in": "query", "name": "otp", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/verifyuser"}}}}, "responses": {"200": {"description": "Verified successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/verifyuser"}}}}, "500": {"description": "Something went wrong", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalError"}}}}}}}, "/checkbeforesignup": {"post": {"description": "Api to verify new users before media signup", "operationId": "checkBeforeSignup", "tags": ["Mobile API"], "parameters": [{"description": "Username", "in": "query", "name": "username", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/checkbeforesignup"}}}}, "responses": {"200": {"description": "Verified successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/checkbeforesignup"}}}}, "500": {"description": "Something went wrong", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalError"}}}}}}}, "/is_logged_in": {"post": {"description": "API to check whether the user is already logged in somewhere.", "operationId": "isLoggedIn", "parameters": [{"name": "username", "description": "Username or Email", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "password", "description": "Password", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "provider", "description": "Provider", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IsLoggedIn"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IsLoggedIn"}}}}, "400": {"description": "Validation Failed.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationFailed"}}}}}}}, "/login": {"post": {"description": "Api to get a token to access public resources, After successful login using user name and password", "operationId": "loginUser", "parameters": [{"description": "Username", "in": "query", "name": "user_name", "required": true, "schema": {"type": "string"}}, {"description": "Password", "in": "query", "name": "password", "required": true, "schema": {"type": "string"}}, {"description": "Device id", "in": "query", "name": "device_id", "required": true, "schema": {"type": "string"}}, {"description": "Device gcm id", "in": "query", "name": "device_gcm_id", "required": true, "schema": {"type": "string"}}, {"description": "Platform / Os", "in": "query", "name": "device_platform", "required": true, "schema": {"type": "string"}}, {"description": "Version", "in": "query", "name": "device_app_version", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/login"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> ", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/login"}}}}, "300": {"description": "Invalid Username or Password", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenError"}}}}, "201": {"description": "Please verify your email.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenError"}}}}}}}, "/loginviasocialmedia": {"post": {"description": "Api to get a token to access public resources, After successful login using user name and password", "operationId": "loginViaSocialMedia", "parameters": [{"description": "Socialmedia verified Username", "in": "query", "name": "user_name", "required": true, "schema": {"type": "string"}}, {"description": "Device id", "in": "query", "name": "device_id", "required": true, "schema": {"type": "string"}}, {"description": "Device gcm id", "in": "query", "name": "device_gcm_id", "required": true, "schema": {"type": "string"}}, {"description": "Platform / Os", "in": "query", "name": "device_platform", "required": true, "schema": {"type": "string"}}, {"description": "Version", "in": "query", "name": "device_app_version", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/loginviasocialmedia"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> ", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loginviasocialmedia"}}}}, "300": {"description": "<PERSON><PERSON><PERSON>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenError"}}}}, "201": {"description": "Please verify your email.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenError"}}}}}}}, "/logout": {"post": {"description": "Api to logout the application", "operationId": "logoutUser", "parameters": [{"description": "token", "in": "query", "name": "access_token", "required": true, "schema": {"type": "string"}}, {"description": "device id", "in": "query", "name": "device_id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/logout"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON>uss ", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/logout"}}}}, "500": {"description": "Something went wrong", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenError"}}}}}}}, "/forgotpassword": {"post": {"description": "Api to change user password", "operationId": "forgotPassword", "parameters": [{"description": "Email id", "in": "query", "name": "email_id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/forgotpassword"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/forgotpassword"}}}}, "500": {"description": "Something went wrong", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalError"}}}}, "201": {"description": "Please verify your email.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenError"}}}}}}}, "/changepassword": {"post": {"description": "Api to change user password", "operationId": "changePassword", "parameters": [{"description": "Otp", "in": "query", "name": "otp", "required": true, "schema": {"type": "string"}}, {"description": "Password", "in": "query", "name": "password", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/changepassword"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/changepassword"}}}}, "500": {"description": "Something went wrong", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalError"}}}}}}}, "/events": {"post": {"description": "Api to get all events", "operationId": "getEvents", "parameters": [{"description": "Access Token", "in": "query", "name": "access_token", "required": true, "schema": {"type": "string"}}, {"description": "Last count", "in": "query", "name": "last_count", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/upcommingevents"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/upcommingevents"}}}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/no_content"}}}}}}}, "/pastevents": {"post": {"description": "Api to get all events", "operationId": "getPastEvents", "parameters": [{"description": "Access Token", "in": "query", "name": "access_token", "required": true, "schema": {"type": "string"}}, {"description": "Last count", "in": "query", "name": "last_count", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/pastevents"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/pastevents"}}}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/no_content"}}}}}}}, "/followevent": {"post": {"description": "Api to add a user into the following list of an event", "operationId": "followEvent", "parameters": [{"description": "Access Token", "in": "query", "name": "access_token", "required": true, "schema": {"type": "string"}}, {"description": "Event id", "in": "query", "name": "event_id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/followevent"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/followevent"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/not_found"}}}}}}}, "/showeventdetails": {"post": {"description": "Api to get event details", "operationId": "showEventDetails", "parameters": [{"description": "Access Token", "in": "query", "name": "access_token", "required": true, "schema": {"type": "string"}}, {"description": "Event Id", "in": "query", "name": "event_id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/showeventdetails"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/showeventdetails"}}}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/no_content"}}}}}}}, "/togetnotification": {"post": {"description": "Api to get new event details after killing the app", "operationId": "getNotification", "parameters": [{"description": "Access Token", "in": "query", "name": "access_token", "required": true, "schema": {"type": "string"}}, {"description": "Event date", "in": "query", "name": "event_date", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/togetnotification"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/togetnotification"}}}}, "201": {"description": "No Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/no_content"}}}}}}}, "/listgenericpoll": {"post": {"description": "Api to get generic poll details", "operationId": "listGenericPoll", "parameters": [{"description": "Access Token", "in": "query", "name": "access_token", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/listgenericpoll"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/listgenericpoll"}}}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/no_content"}}}}}}}, "/listlivepolls": {"post": {"description": "Api to get live poll details", "operationId": "listLivePolls", "parameters": [{"description": "Access Token", "in": "query", "name": "access_token", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/listlivepoll"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/listlivepoll"}}}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/no_content"}}}}}}}, "/attendpolls": {"post": {"description": "Api to attend polls", "operationId": "attendPolls", "parameters": [{"description": "Access Token", "in": "query", "name": "access_token", "required": true, "schema": {"type": "string"}}, {"description": "Poll Id", "in": "query", "name": "poll_id", "required": true, "schema": {"type": "string"}}, {"description": "Answer", "in": "query", "name": "answer", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/attendpoll"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/attendpoll"}}}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/no_content"}}}}}}}, "/resources": {"post": {"description": "Api to list resources", "operationId": "listResources", "parameters": [{"description": "Access Token", "in": "query", "name": "access_token", "required": true, "schema": {"type": "string"}}, {"description": "Sub Catagory", "in": "query", "name": "sub_catagory", "required": true, "schema": {"type": "string"}}, {"description": "Search", "in": "query", "name": "search", "required": false, "schema": {"type": "string"}}, {"description": "Last count", "in": "query", "name": "last_count", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/resources"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/resources"}}}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/no_content"}}}}}}}, "/getLatestFeaturedPost": {"post": {"description": "Api to list resources", "operationId": "featuredPost", "parameters": [{"description": "Access Token", "in": "query", "name": "access_token", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/getLatestFeaturedPost"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/getLatestFeaturedPost"}}}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/no_content"}}}}}}}, "/is_subscribed": {"post": {"description": "API for checking subscription status", "operationId": "isSubscribed", "parameters": [{"description": "token", "in": "query", "name": "access_token", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IsSubscribed"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> ", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IsSubscribed"}}}}, "500": {"description": "Something went wrong", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenError"}}}}}}}}, "components": {"schemas": {"AppNotification": {"type": "object", "properties": {"version": {"type": "string"}, "platform": {"type": "string"}, "device_id": {"type": "string"}}}, "ValidationFailed": {"type": "object", "properties": {"message": {"type": "string"}}}, "signupviasocialmedia": {"type": "object", "properties": {"user_type": {"type": "string"}, "username": {"type": "string"}, "name": {"type": "string"}, "email_id": {"type": "string"}, "dob": {"type": "string"}, "gender": {"type": "string"}, "media_name": {"type": "string"}}}, "InternalError": {"type": "object", "properties": {"message": {"type": "string"}}}, "emailexists": {"type": "object", "properties": {"message": {"type": "string"}}}, "verifyuser": {"type": "object", "properties": {"message": {"type": "string"}}}, "checkbeforesignup": {"type": "object", "properties": {"message": {"type": "string"}}}, "IsLoggedIn": {"type": "object", "properties": {"message": {"type": "string"}}}, "login": {"type": "object", "properties": {"user_name": {"type": "string"}, "password": {"type": "string"}, "device_id": {"type": "string"}, "device_gcm_id": {"type": "string"}, "device_platform": {"type": "string"}, "device_app_version": {"type": "string"}}}, "loginviasocialmedia": {"type": "object", "properties": {"user_name": {"type": "string"}, "device_id": {"type": "string"}, "device_gcm_id": {"type": "string"}, "device_platform": {"type": "string"}, "device_app_version": {"type": "string"}}}, "logout": {"type": "object", "properties": {"access_token": {"type": "string"}, "device_id": {"type": "string"}}}, "forgotpassword": {"type": "object", "properties": {"email_id": {"type": "string"}}}, "changepassword": {"type": "object", "properties": {"otp": {"type": "string"}, "password": {"type": "string"}}}, "upcommingevents": {"type": "object", "properties": {"message": {"type": "string"}}}, "no_content": {"type": "object", "properties": {"message": {"type": "string"}}}, "followevent": {"type": "object", "properties": {"message": {"type": "string"}}}, "not_found": {"type": "object", "properties": {"message": {"type": "string"}}}, "showeventdetails": {"type": "object", "properties": {"message": {"type": "string"}}}, "togetnotification": {"type": "object", "properties": {"message": {"type": "string"}}}, "listgenericpoll": {"type": "object", "properties": {"message": {"type": "string"}}}, "listlivepoll": {"type": "object", "properties": {"message": {"type": "string"}}}, "attendpoll": {"type": "object", "properties": {"message": {"type": "string"}}}, "resources": {"type": "object", "properties": {"message": {"type": "string"}}}, "getLatestFeaturedPost": {"type": "object", "properties": {"message": {"type": "string"}}}, "IsSubscribed": {"type": "object", "properties": {"message": {"type": "string"}}}, "TokenError": {"type": "object", "properties": {"message": {"type": "string"}}}, "signup": {"type": "object", "properties": {"user_type": {"type": "string"}, "username": {"type": "string"}, "name": {"type": "string"}, "email_id": {"type": "string"}, "dob": {"type": "string"}, "gender": {"type": "string"}}}, "pastevents": {"type": "object", "properties": {"message": {"type": "string"}}}}}}