<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SignupController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\PollController;
use App\Http\Controllers\ResourceController;

Route::middleware(['api'])->group(function () {
    // Test endpoint for debugging
    Route::post('/test-db', function() {
        try {
            // Test 1: Check if PDO MySQL is available
            if (!extension_loaded('pdo_mysql')) {
                return response()->json(['error' => 'PDO MySQL extension not loaded']);
            }

            // Test 2: Test direct PDO connection
            $pdo = new PDO('mysql:host=**********;port=3306;dbname=bestrong', 'root', 'fin123');

            // Test 3: Test Laravel DB connection
            $result = DB::select('SELECT 1 as test');

            return response()->json([
                'status' => 'success',
                'pdo_mysql_loaded' => extension_loaded('pdo_mysql'),
                'direct_connection' => 'success',
                'laravel_connection' => 'success',
                'test_result' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    });

    // Public (no token)
    Route::post('/signup', [SignupController::class, 'index']);
    Route::post('/signupviasocialmedia', [SignupController::class, 'signupViaSocialMedia']);
    Route::post('/login', [LoginController::class, 'login']);
    Route::post('/app-notification', [LoginController::class, 'appNotification']);
    Route::post('/loginviasocialmedia', [LoginController::class, 'loginViaSocialMedia']);
    Route::post('/verifyuser', [SignupController::class, 'activateUser']);
    Route::post('/changepassword', [SignupController::class, 'changePassword']);
    Route::post('/forgotpassword', [SignupController::class, 'forgotPassword']);
    Route::post('/checkbeforesignup', [SignupController::class, 'chkBeforeSignup']);
    Route::post('/is_logged_in', [LoginController::class, 'is_logged_in']);

    // Token protected
    Route::middleware(['tokenMatch'])->group(function () {
        Route::post('/events', [EventController::class, 'index']);
        Route::post('/pastevents', [EventController::class, 'getPastEvents']);
        Route::post('/followevent', [EventController::class, 'followEvent']);
        Route::post('/showeventdetails', [EventController::class, 'getEventDetails']);
        Route::post('/listgenericpoll', [PollController::class, 'listGenericPolls']);
        Route::post('/listlivepolls', [PollController::class, 'listLivePolls']);
        Route::post('/attendpolls', [PollController::class, 'attendPolls']);
        Route::post('/resources', [ResourceController::class, 'index']);
        Route::post('/togetnotification', [EventController::class, 'toGetNotification']);
        Route::post('/logout', [LoginController::class, 'logout']);
        Route::post('/getLatestFeaturedPost', [ResourceController::class, 'getLatestFeaturedPost']);
        Route::post('/is_subscribed', [ResourceController::class, 'is_subscribed']);
    });
});