<?php

namespace App\Http\Controllers;
use Request;

use Illuminate\Database\Eloquent\ModelNotFoundException;
use DB;
use Response;

class ResourceController extends Controller {
    
    public function index() {

        $server_url = site_url();
        $arr = array();
        $results = array();
        $catagry = array();
        $catagry_list_array_terms = array();
        $token = Request::get('access_token');
        $sub_cata = Request::get('sub_catagory');
        $firstCount = Request::get('last_count');
        $search_str = Request::get('search'); 

        $arr['StatusCode'] = 200;

        //$sub_cata
        /*1 = student
            2 = teacher
            3 = parent
            4 = school admin
        */

        $sub_cata = strtolower($sub_cata);    
        if ($sub_cata != '') {
            $cata = $sub_cata;
        }else{
            $cata = 'student';
        }   
        
        if($cata == 'supporter')
        {
            $cata_taxonamy = DB::select('select DISTINCT(wt.name), wt.term_id FROM `' . $this->wp_prefix . 'terms` as wt join `' . $this->wp_prefix . 'term_taxonomy` as wtt on wt.term_id = wtt.term_id WHERE wtt.taxonomy="category"  and wt.name IN ("student","teacher","parent","school admin")');
        }
        else
        {
            $cata_taxonamy = DB::select('select DISTINCT(wt.name), wt.term_id FROM `' . $this->wp_prefix . 'terms` as wt join `' . $this->wp_prefix . 'term_taxonomy` as wtt on wt.term_id = wtt.term_id WHERE wtt.taxonomy="category"  and wt.name =?', array($cata));
        }
        
        $i = 0;

        foreach ($cata_taxonamy as $catagry) {
            $catagry_list[$i]['key'] = $catagry->name;
            $catagry_list[$i]['value'] = $catagry->term_id;
            $catagry_list_array_terms[] = $catagry->term_id;
            $i++;

            $sub_cat_id = $catagry->term_id;

            $categories = DB::select('select DISTINCT(wpt.name), wpt.term_id FROM `' . $this->wp_prefix . 'terms` as wt join `' . $this->wp_prefix . 'term_taxonomy` as wtt on wt.term_id = wtt.parent join `' . $this->wp_prefix . 'terms` as wpt on wpt.term_id= wtt.term_taxonomy_id WHERE wt.term_id ='.$sub_cat_id);             
            foreach($categories as $sub_category) { 

                $catagry_list[$i]['key'] = $sub_category->name;
                $catagry_list[$i]['value'] = $sub_category->term_id;
                $catagry_list_array_terms[] = $sub_category->term_id;
                $i++; 
            }            
        }
        $arr['catagery'] = $catagry_list;

        if($cata != 'supporter'){
            $cQuery = DB::table($this->wp_prefix . 'posts');
            if($search_str) {      
                $cQuery->where('post_title', 'like', '%' . $search_str . '%');
            }
            $cQuery->where('post_status', 'publish')
                    ->whereIn('term_taxonomy_id', $catagry_list_array_terms)
                    ->join($this->wp_prefix . 'term_relationships', $this->wp_prefix . 'posts.ID', '=', $this->wp_prefix . 'term_relationships.object_id');
            $count  = $cQuery->count();
            $lastCount = $firstCount + 15;

            $query = DB::table($this->wp_prefix . 'posts');
            $query  ->whereIn($this->wp_prefix .'term_relationships.term_taxonomy_id', $catagry_list_array_terms)
                    ->where('post_status', 'publish');

            if($search_str) {      
                $query->where('post_title', 'like', '%' . $search_str . '%');
                $query->orWhere($this->wp_prefix.'terms.name', 'like', '%' . $search_str . '%');

            }

            $query->whereNull('membership_id')
                    ->join($this->wp_prefix . 'term_relationships', $this->wp_prefix . 'posts.ID', '=', $this->wp_prefix . 'term_relationships.object_id')
                    ->leftJoin($this->wp_prefix . 'pmpro_memberships_pages', $this->wp_prefix . 'posts.ID', '=', $this->wp_prefix . 'pmpro_memberships_pages.page_id')
                   ->join($this->wp_prefix . 'term_taxonomy', $this->wp_prefix . 'term_relationships.term_taxonomy_id', '=', $this->wp_prefix . 'term_taxonomy.term_taxonomy_id')
                   ->join($this->wp_prefix . 'terms', $this->wp_prefix . 'terms.term_id', '=', $this->wp_prefix . 'term_taxonomy.term_id')
                    ->select('*')
                    ->distinct('ID')
                    ->orderBy('post_date', 'desc');
            $top_posts = $query->get();
        } else {
            $cQuery = DB::table($this->wp_prefix . 'posts');
            if($search_str) {      
                $cQuery->where('post_title', 'like', '%' . $search_str . '%');
            }
            $cQuery->where('post_status', 'publish')
                    ->whereIn('term_taxonomy_id', $catagry_list_array_terms)
                    ->join($this->wp_prefix . 'term_relationships', $this->wp_prefix . 'posts.ID', '=', $this->wp_prefix . 'term_relationships.object_id');
            $count  = $cQuery->count();
            $lastCount = $firstCount + 15;

            $query = DB::table($this->wp_prefix . 'posts');
            $query  ->whereIn($this->wp_prefix .'term_relationships.term_taxonomy_id', $catagry_list_array_terms)
                    ->where('post_status', 'publish');

            if($search_str) {   
                $query->where('post_title', 'like', '%' . $search_str . '%');
                $query->orWhere($this->wp_prefix.'terms.name', 'like', '%' . $search_str . '%');

            }

            $query->whereNull('membership_id')
                    ->join($this->wp_prefix . 'term_relationships', $this->wp_prefix . 'posts.ID', '=', $this->wp_prefix . 'term_relationships.object_id')
                    ->leftJoin($this->wp_prefix . 'pmpro_memberships_pages', $this->wp_prefix . 'posts.ID', '=', $this->wp_prefix . 'pmpro_memberships_pages.page_id')
                    ->join($this->wp_prefix . 'term_taxonomy', $this->wp_prefix . 'term_relationships.term_taxonomy_id', '=', $this->wp_prefix . 'term_taxonomy.term_taxonomy_id')
                   ->join($this->wp_prefix . 'terms', $this->wp_prefix . 'terms.term_id', '=', $this->wp_prefix . 'term_taxonomy.term_id')
                    ->select('*')
                    ->distinct('ID')
                    ->orderBy('post_date', 'desc');
            $top_posts = $query->get();
        }
       
        $full_ids = array();

        if ($top_posts) {
            $topposts = array();
            foreach ($top_posts as $top) {
                if (in_array($top->ID, $full_ids)) {
                    continue;
                }else{
                    array_push($full_ids, $top->ID);
                    $tops['post_id'] = $id = $top->ID;
                    $tops['post_title'] = $top->post_title;
                    $tops['post_desc'] = $top->post_excerpt;
                    $tops['post_img'] = $this->getFeaturedImage($id);
                    $tops['is_premium'] = $top->membership_id != "" ? true : false;
                    $tops['post_url'] = $server_url."/blog_mobapp/blog.php?p=" . $top->ID;
                    $tops['lastCnt'] = $lastCount;
                    $topposts[] = $tops;
                }    
            }
            $top_posts = $topposts;
        }

        if ($count < $lastCount)
            $lastCount = $count;
        $top_posts = array_slice($top_posts, $firstCount, 15);
        if ($top_posts) {
            $topposts = array();
            foreach ($top_posts as $top) {
                $tops['post_id'] = $id = $top['post_id'];
                $tops['post_title'] = $top['post_title'];
                $tops['post_desc'] = $top['post_desc'];
                $tops['post_img'] = $this->getFeaturedImage($id);
                $tops['is_premium'] = $top['is_premium'];
                $tops['post_url'] = $top['post_url'];
                $tops['lastCnt'] = $lastCount;
                $topposts[] = $tops;
            }

            $arr['posts'] = $topposts;
        } else {
            $arr['posts'] = '';
            $arr['Message'] = 'No posts to show';
            $arr['StatusCode'] = 201;
        }
        return response()->json($arr);
    }

    public function getFeaturedImage($id) {
        $thumb = wp_get_attachment_image_src(get_post_thumbnail_id($id), 'medium');
        if ($thumb) {
            return $thumb['0'];
        } else {
            return false;
        }
    }
    /*
     * Get featured images for latest post
     */
    public function getFeaturedImageLatest($id){
        $thumb = wp_get_attachment_image_src(get_post_thumbnail_id($id), 'large');
        if ($thumb['0']) {
            return $thumb['0'];
        } else {
            return false;
        }
    }

    public function getPostImage($id) {
        $image = DB::select('select guid from ' . $this->wp_prefix . 'posts WHERE ID =' . $id);
        if ($image) {
            return $image[0]->guid;
        } else
            return 0;
    }

    public function getLatestFeaturedPost() {

        $server_url = site_url();

        $arr = array();
        $results = array();
        $catagry = array();

        $cata = "featured";
        $cata_taxonamy = DB::select('select DISTINCT(wt.name), wt.term_id FROM ' . $this->wp_prefix . 'terms as wt  WHERE wt.name =?', array($cata));
        $catagry_list[0]['key'] = 'All';
        $catagry_list[0]['value'] = '0';
        $i = 1;
        foreach ($cata_taxonamy as $catagry) {
            $catagry_list[$i]['key'] = $catagry->name;
            $catagry_list[$i]['value'] = $catagry->term_id;
            $catagry_list_array_terms[] = $catagry->term_id;
            $i++;
        }
        $top_posts = DB::table($this->wp_prefix . 'posts')
                ->take(1)
                ->orderBy("post_date", "desc")
                ->whereIn('term_taxonomy_id', $catagry_list_array_terms)
                ->where('post_status', 'publish')
                ->join($this->wp_prefix . 'term_relationships', $this->wp_prefix . 'posts.ID', '=', $this->wp_prefix . 'term_relationships.object_id')
                ->select('*')
                ->distinct('ID')
                ->get();                 

        if ($top_posts) {
            foreach ($top_posts as $top) {
                //foreach($top as $post){
                $tops['post_id'] = $id = $top->ID;
                $tops['post_title'] = $top->post_title;
                $tops['post_desc'] = $top->post_excerpt;
                $tops['post_img'] = $this->getFeaturedImageLatest($id);
                $tops['post_url'] = $server_url."/blog_mobapp/blog.php?p=" . $top->ID;
                $tops['lastCnt'] = 0;
                //}
                $topposts[] = $tops;
            }
            $arr['posts'] = $topposts;
            $arr['StatusCode'] = 200;
        } else {
            $arr['posts'] = '';
            $arr['Message'] = 'No posts to show';
            $arr['StatusCode'] = 201;
        }

        return response()->json($arr);
    }
    
    public function is_subscribed(){
        $response['result'] = pmpro_hasMembershipLevel($this->userType, $this->user->ID);
        if(!$response['result'] && true){
            $userDevice = DB::table('wp_user_device_details')->where('device_user_id', $this->user->ID)->pluck('device_platform');
            if($userDevice == "ios"){
                $premium_mail = get_user_meta($this->user->ID, 'bsog_premium_content_mail_sent', true);
                if(!$premium_mail){
                    $mail = Mail::send('emails.premium_content', ['user' => $this->user], function($message) {
                        $userType = ucwords(self::$userTypes[$this->userType]);
                        if($userType == 'School-admin'){
                            $userTypeResource = 'School Administrators';
                        }
                        elseif($userType == 'Teacher'){
                            $userTypeResource = 'Teachers';
                        }
                        elseif($userType == 'Parent'){
                            $userTypeResource = 'Parents';
                        }
                        elseif($userType == 'Supporter'){
                            $userTypeResource = 'Supporters';
                        }
                        elseif($userType == 'Student'){
                            $userTypeResource = 'Students';
                        }
                        $message->to($this->user->user_email, $this->user->display_name)
                                ->from('<EMAIL>', 'Be Strong')
                                ->subject("Access to Insider Resources for $userTypeResource from Be Strong!");
                    });
                    update_user_meta($this->user->ID, 'bsog_premium_content_mail_sent', true);
                }                
            }
        }
        return response()->json($response);
    }
}
?>    
